const https = require('https');
const fs = require('fs');
const path = require('path');

const images = [
    {
        name: 'healthcare-ai.jpg',
        query: 'medical-technology-ai',
        description: 'Healthcare AI technology'
    },
    {
        name: 'finance-ai.jpg',
        query: 'financial-technology-dashboard',
        description: 'Financial services AI'
    },
    {
        name: 'retail-ai.jpg',
        query: 'ecommerce-shopping-technology',
        description: 'Retail AI solutions'
    },
    {
        name: 'manufacturing-ai.jpg',
        query: 'smart-factory-automation',
        description: 'Manufacturing AI'
    },
    {
        name: 'realestate-ai.jpg',
        query: 'real-estate-technology',
        description: 'Real estate AI'
    },
    {
        name: 'education-ai.jpg',
        query: 'digital-learning-technology',
        description: 'Education AI'
    }
];

const downloadImage = (url, filename) => {
    return new Promise((resolve, reject) => {
        https.get(url, (response) => {
            if (response.statusCode === 200) {
                const fileStream = fs.createWriteStream(filename);
                response.pipe(fileStream);
                fileStream.on('finish', () => {
                    fileStream.close();
                    resolve();
                });
            } else {
                reject(new Error(`Failed to download ${filename}`));
            }
        }).on('error', reject);
    });
};

const downloadAllImages = async () => {
    const outputDir = path.join(__dirname, 'assets', 'images', 'use-cases');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    for (const image of images) {
        const url = `https://source.unsplash.com/featured/?${image.query}`;
        const filename = path.join(outputDir, image.name);
        
        try {
            console.log(`Downloading ${image.description}...`);
            await downloadImage(url, filename);
            console.log(`Successfully downloaded ${image.name}`);
        } catch (error) {
            console.error(`Error downloading ${image.name}:`, error);
        }
    }
};

downloadAllImages(); 