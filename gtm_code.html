

    <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M5H3KB45"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    =========================

      
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);
    
                gtag('consent', 'default', {
                    'ad_storage': 'denied',
                    'ad_user_data': 'denied',
                    'ad_personalization': 'denied',
                    'analytics_storage': 'denied',
                });
    
                if('Check consent status on browser cookie or localStorage') {
                gtag('consent', 'update', {
                    'ad_storage': ad_storage_consent, // granted or denied
                    'ad_user_data': ad_user_data_consent,
                    'ad_personalization': ad_personalization_consent,
                    'analytics_storage': analytics_storage_consent,
                });
            } 
        </script>
    
       <!-- Google Tag Manager -->
        <script>
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-M5H3KB45');
        </script>
        <!-- End Google Tag Manager -->
    
        <script>
            // Cookie consent banner
            consentButton.addEventListener('click', function() {
                // Update consent
                gtag('consent', 'update', {
                    'ad_storage': ad_storage_consent, // granted or denied
                    'ad_user_data': ad_user_data_consent,
                    'ad_personalization': ad_personalization_consent,
                    'analytics_storage': analytics_storage_consent,
                });
    
                // Save consent status on browser
                document.cookie = "CookieConsent={necessary:true, statistics:true, marketing:true}";
            });
        </script>
    </head>

    

=========================

<script>    
    window.dataLayer = window.dataLayer || [];  
    function gtag(){dataLayer.push(arguments);}  
        gtag('consent', 'default', {  
        'ad_storage': 'denied',  
        'am_user_data': 'denied',  
        'ad_personalization': 'denied',  
        'analytics_storage': 'denied'  
    });  
}  
</script>  

<script>  
// check consent status on browser cookie or localStorage  
gtag('consent', 'update', {  
    'ad_storage': ad_storage_consent, // granted or denied  
    'ad_user_data': ad_user_data_consent,  
    'ad_personalization': ad_personalization_consent,  
    'analytics_storage': analytics_storage_consent  
});  
</script>  

// Google Tag Manager  
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':  
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],  
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.src=  
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);  
})(window,document,'script','dataLayer','GTM-XXXXXX');</script>  

<script>  
// cookie consent banner  
consentButton.addEventListener('click', function() {  
    // update consent  
    gtag('consent', 'update', {  
        'ad_storage': ad_storage_consent, // granted or denied  
        'ad_user_data': ad_user_data_consent,  
        'ad_personalization': ad_personalization_consent,  
        'analytics_storage': analytics_storage_consent  
    });  
});  

// save consent status on user browser  
document.cookie = "CookieConsent = {necessary:true, statistics:true, marketing:true}";  
</script>  
