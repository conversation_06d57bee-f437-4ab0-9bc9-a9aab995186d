<?php
// Set error reporting for debugging (remove in production)
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

// Only process POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data and sanitize
    $name = isset($_POST['contact-name']) ? filter_var($_POST['contact-name'], FILTER_SANITIZE_STRING) : '';
    $email = isset($_POST['contact-email']) ? filter_var($_POST['contact-email'], FILTER_SANITIZE_EMAIL) : '';
    $phone = isset($_POST['contact-phone']) ? filter_var($_POST['contact-phone'], FILTER_SANITIZE_STRING) : '';
    $message = isset($_POST['contact-message']) ? filter_var($_POST['contact-message'], FILTER_SANITIZE_STRING) : '';
    
    // Basic validation
    if (empty($name) || empty($email) || empty($message) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response = array(
            'status' => 'error',
            'message' => 'Please fill all required fields with valid information'
        );
        echo json_encode($response);
        exit;
    }
    
    // Email details
    $to = '<EMAIL>';
    $subject = 'New Contact Form Submission from ' . $name;
    
    // Email content
    $email_content = "Name: $name\n";
    $email_content .= "Email: $email\n";
    
    if (!empty($phone)) {
        $email_content .= "Phone: $phone\n";
    }
    
    $email_content .= "Message:\n$message\n";
    
    // Email headers
    $headers = "From: $name <$email>\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Send email
    if (mail($to, $subject, $email_content, $headers)) {
        $response = array(
            'status' => 'success',
            'message' => 'Thank you for your message! We will get back to you soon.'
        );
    } else {
        $response = array(
            'status' => 'error',
            'message' => 'Sorry, something went wrong. Please try again later.'
        );
    }
    
    // Return response as JSON
    echo json_encode($response);
} else {
    // Not a POST request
    header('HTTP/1.1 403 Forbidden');
    echo 'Access forbidden';
}
?> 